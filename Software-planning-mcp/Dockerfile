# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use the official Node.js image.
FROM node:18-alpine

# Set the working directory inside the container.
WORKDIR /app

# Copy over only package.json and pnpm-lock.yaml to install dependencies.
COPY package.json pnpm-lock.yaml ./

# Install pnpm globally and then install dependencies.
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# Copy the rest of the application code.
COPY . .

# Build the application.
RUN pnpm run build

# Change the permissions for the build script to make it executable.
RUN chmod +x build/index.js

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
ENTRYPOINT ["node", "build/index.js"]
